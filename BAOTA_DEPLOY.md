# 宝塔服务器部署指南

## 🚀 部署步骤

### 第一步：准备宝塔环境

1. **安装必要软件**
   ```
   宝塔面板 → 软件商店 → 搜索并安装：
   - Node.js版本管理器 (推荐安装 Node.js 16+ 版本)
   - PM2管理器 (进程管理)
   - Nginx (可选，用于反向代理)
   ```

2. **创建网站目录**
   ```
   宝塔面板 → 文件 → 创建目录：
   /www/wwwroot/bsc-shib-api
   ```

### 第二步：上传项目文件

1. **压缩项目文件**
   - 将整个项目文件夹压缩为 `bsc-shib-api.zip`
   - 包含所有文件：src/, package.json, .env.production 等

2. **上传到服务器**
   ```
   宝塔面板 → 文件 → 进入 /www/wwwroot/bsc-shib-api
   → 上传 → 选择 bsc-shib-api.zip → 解压
   ```

### 第三步：配置环境

1. **进入项目目录**
   ```bash
   cd /www/wwwroot/bsc-shib-api
   ```

2. **运行部署脚本**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

3. **配置环境变量**
   ```bash
   # 复制生产环境配置
   cp .env.production .env
   
   # 编辑配置文件
   vi .env
   ```

   **重要配置项：**
   ```bash
   # 生产环境不需要代理
   USE_PROXY=false
   
   # 选择稳定的BSC RPC节点
   BSC_RPC_URL=https://bsc-dataseed1.binance.org/
   
   # 设置端口 (确保防火墙已开放)
   PORT=3000
   
   # 生产环境
   NODE_ENV=production
   ```

### 第四步：安装依赖和启动

1. **安装项目依赖**
   ```bash
   npm install --production
   ```

2. **测试网络连接**
   ```bash
   npm run test
   ```

3. **使用PM2启动应用**
   ```bash
   # 启动应用
   npm run pm2:start
   
   # 查看状态
   pm2 status
   
   # 查看日志
   npm run pm2:logs
   ```

4. **设置开机自启**
   ```bash
   pm2 startup
   pm2 save
   ```

### 第五步：配置防火墙和反向代理

1. **开放端口**
   ```
   宝塔面板 → 安全 → 添加端口规则：
   端口：3000
   协议：TCP
   备注：BSC SHIB API
   ```

2. **配置Nginx反向代理 (可选)**
   ```
   宝塔面板 → 网站 → 添加站点：
   域名：api.yourdomain.com
   根目录：/www/wwwroot/bsc-shib-api
   
   然后配置反向代理：
   目标URL：http://127.0.0.1:3000
   ```

   **Nginx配置示例：**
   ```nginx
   location / {
       proxy_pass http://127.0.0.1:3000;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
   }
   ```

### 第六步：测试API

1. **健康检查**
   ```bash
   curl http://your-server-ip:3000/api/health
   ```

2. **API信息**
   ```bash
   curl http://your-server-ip:3000/api/info
   ```

3. **测试SHIB转账查询**
   ```bash
   curl http://your-server-ip:3000/api/transfer/YOUR_TX_HASH
   ```

## 🔧 常用管理命令

```bash
# 查看应用状态
pm2 status

# 重启应用
npm run pm2:restart

# 停止应用
npm run pm2:stop

# 查看实时日志
npm run pm2:logs

# 查看错误日志
tail -f logs/err.log

# 查看访问日志
tail -f logs/out.log

# 更新代码后重启
git pull  # 如果使用git
npm run pm2:restart
```

## 🛠️ 故障排除

### 1. 端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep :3000

# 修改端口
vi .env  # 修改 PORT=3001
npm run pm2:restart
```

### 2. 网络连接问题
```bash
# 测试BSC网络连接
curl -X POST https://bsc-dataseed1.binance.org/ \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}'

# 尝试其他RPC节点
vi .env  # 修改 BSC_RPC_URL
npm run pm2:restart
```

### 3. 内存不足
```bash
# 查看内存使用
free -h

# 调整PM2配置
vi ecosystem.config.js  # 修改 max_memory_restart
npm run pm2:restart
```

## 📊 监控和维护

1. **设置日志轮转**
   ```bash
   # 安装logrotate
   vi /etc/logrotate.d/bsc-shib-api
   ```

2. **监控API状态**
   - 使用宝塔面板的监控功能
   - 设置API健康检查定时任务

3. **定期更新**
   - 定期检查依赖更新
   - 监控BSC网络状态
   - 备份重要配置

## 🎯 生产环境优化

1. **性能优化**
   ```javascript
   // 在 ecosystem.config.js 中调整
   instances: 'max',  // 使用所有CPU核心
   exec_mode: 'cluster'  // 集群模式
   ```

2. **安全配置**
   - 设置防火墙规则
   - 使用HTTPS (配置SSL证书)
   - 限制API访问频率

3. **备份策略**
   - 定期备份配置文件
   - 监控日志文件大小
   - 设置自动重启策略

## 📞 技术支持

如果遇到问题，请检查：
1. 日志文件：`logs/err.log`
2. PM2状态：`pm2 status`
3. 网络连接：`npm run test`
4. 端口状态：`netstat -tlnp | grep :3000`
