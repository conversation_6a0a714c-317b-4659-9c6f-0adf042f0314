# BSC SHIB Transfer Tracker API

一个用于查询BSC链上SHIB代币转账数据的Node.js API服务。

## 功能特性

- 🔍 根据交易哈希查询SHIB转账详情
- 📊 支持批量查询多个交易
- 🌐 连接BSC (Binance Smart Chain) 主网
- 🪙 专门针对SHIB代币优化
- 📝 详细的转账信息包括金额、发送方、接收方等
- ⚡ 快速响应和错误处理
- 📋 完整的API文档

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env` 文件并根据需要修改配置：

```bash
# BSC网络配置
BSC_RPC_URL=https://bsc-dataseed1.binance.org/
SHIB_CONTRACT_ADDRESS=0x2859e4544C4bB03966803b044A93563Bd2D0DD4D

# API服务配置
PORT=3000
NODE_ENV=development
```

### 3. 启动服务

```bash
# 开发模式 (自动重启)
npm run dev

# 生产模式
npm start
```

服务启动后访问: http://localhost:3000

## API 接口

### 基础信息

- **基础URL**: `http://localhost:3000/api`
- **响应格式**: JSON
- **支持的网络**: BSC主网

### 接口列表

#### 1. 健康检查
```
GET /api/health
```

#### 2. API信息
```
GET /api/info
```

#### 3. 查询单个转账
```
GET /api/transfer/:hash
```

**参数:**
- `hash`: 交易哈希 (必需)

**示例:**
```bash
curl http://localhost:3000/api/transfer/0x1234567890abcdef...
```

#### 4. 批量查询转账
```
POST /api/transfer/batch
```

**请求体:**
```json
{
  "hashes": [
    "0x1234567890abcdef...",
    "0xabcdef1234567890..."
  ]
}
```

**示例:**
```bash
curl -X POST http://localhost:3000/api/transfer/batch \
  -H "Content-Type: application/json" \
  -d '{"hashes":["0x1234...","0x5678..."]}'
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    "transactionHash": "0x...",
    "blockNumber": 12345678,
    "blockHash": "0x...",
    "confirmations": 100,
    "status": "success",
    "timestamp": **********,
    "date": "2022-01-01T00:00:00.000Z",
    "gasUsed": "21000",
    "gasPrice": "**********",
    "shibTransfers": [
      {
        "from": "0x...",
        "to": "0x...",
        "amount": "1000000.0",
        "amountWei": "1000000000000000000000000",
        "token": "SHIB",
        "logIndex": 0
      }
    ],
    "totalTransfers": 1,
    "network": "BSC",
    "tokenContract": "0x2859e4544C4bB03966803b044A93563Bd2D0DD4D"
  },
  "message": "Found 1 SHIB transfer(s)"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "message": "Transaction not found",
    "code": "TX_NOT_FOUND"
  },
  "data": null
}
```

## 错误代码

| 代码 | 描述 |
|------|------|
| `INVALID_HASH` | 无效的交易哈希格式 |
| `TX_NOT_FOUND` | 交易未找到 |
| `RECEIPT_NOT_FOUND` | 交易收据未找到 |
| `MISSING_PARAMETER` | 缺少必需参数 |
| `BATCH_ERROR` | 批量查询错误 |
| `INTERNAL_ERROR` | 内部服务器错误 |

## 项目结构

```
src/
├── index.js          # 主应用入口
├── config.js         # 配置文件
├── blockchain.js     # 区块链服务
├── transferService.js # 转账查询服务
├── routes.js         # API路由
└── logger.js         # 日志记录器
```

## 技术栈

- **Node.js** - 运行时环境
- **Express.js** - Web框架
- **ethers.js** - 以太坊库
- **BSC** - Binance Smart Chain
- **CORS** - 跨域支持

## 开发

### 开发模式
```bash
npm run dev
```

### 生产部署
```bash
npm start
```

## 注意事项

1. 确保网络连接稳定，能够访问BSC RPC节点
2. SHIB合约地址已预配置为BSC主网地址
3. API有速率限制，批量查询最多支持10个交易
4. 建议在生产环境中使用专用的RPC节点

## 许可证

MIT License
