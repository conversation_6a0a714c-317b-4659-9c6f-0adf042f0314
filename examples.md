# API 使用示例

## 1. 健康检查

```bash
curl http://localhost:3000/api/health
```

**响应:**
```json
{
  "success": true,
  "message": "BSC SHIB Transfer API is running",
  "timestamp": "2025-08-18T07:22:47.465Z",
  "version": "1.0.0"
}
```

## 2. 获取API信息

```bash
curl http://localhost:3000/api/info
```

## 3. 查询单个SHIB转账

```bash
curl http://localhost:3000/api/transfer/0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
```

**注意:** 请替换为真实的BSC网络上的SHIB转账交易哈希。

## 4. 批量查询SHIB转账

```bash
curl -X POST http://localhost:3000/api/transfer/batch \
  -H "Content-Type: application/json" \
  -d '{
    "hashes": [
      "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
      "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
    ]
  }'
```

## 5. JavaScript 示例

```javascript
// 使用 fetch API
async function getShibTransfer(txHash) {
  try {
    const response = await fetch(`http://localhost:3000/api/transfer/${txHash}`);
    const data = await response.json();
    
    if (data.success) {
      console.log('SHIB转账信息:', data.data);
      data.data.shibTransfers.forEach(transfer => {
        console.log(`从 ${transfer.from} 转账 ${transfer.amount} SHIB 到 ${transfer.to}`);
      });
    } else {
      console.error('查询失败:', data.error.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 使用示例
getShibTransfer('YOUR_TX_HASH_HERE');
```

## 6. Python 示例

```python
import requests
import json

def get_shib_transfer(tx_hash):
    url = f"http://localhost:3000/api/transfer/{tx_hash}"
    
    try:
        response = requests.get(url)
        data = response.json()
        
        if data['success']:
            print("SHIB转账信息:", json.dumps(data['data'], indent=2))
            for transfer in data['data']['shibTransfers']:
                print(f"从 {transfer['from']} 转账 {transfer['amount']} SHIB 到 {transfer['to']}")
        else:
            print("查询失败:", data['error']['message'])
            
    except Exception as e:
        print("请求错误:", str(e))

# 使用示例
get_shib_transfer('YOUR_TX_HASH_HERE')
```

## 7. 如何获取真实的SHIB转账哈希

1. 访问 [BSCScan](https://bscscan.com/)
2. 搜索SHIB代币合约地址: `0x2859e4544C4bB03966803b044A93563Bd2D0DD4D`
3. 在"Transfers"标签页中找到最近的转账交易
4. 复制交易哈希用于测试

## 8. 常见错误处理

### 无效哈希格式
```json
{
  "success": false,
  "error": {
    "message": "Invalid transaction hash format",
    "code": "INVALID_HASH"
  },
  "data": null
}
```

### 交易未找到
```json
{
  "success": false,
  "error": {
    "message": "Transaction not found",
    "code": "TX_NOT_FOUND"
  },
  "data": null
}
```

### 无SHIB转账
```json
{
  "success": true,
  "data": {
    "transactionHash": "0x...",
    "shibTransfers": [],
    "totalTransfers": 0
  },
  "message": "No SHIB transfers found in this transaction"
}
```
