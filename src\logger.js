/**
 * 简单的日志记录器
 */
class Logger {
  constructor() {
    this.levels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    this.currentLevel = this.levels.INFO;
  }

  /**
   * 格式化日志消息
   * @param {string} level - 日志级别
   * @param {string} message - 消息
   * @param {Object} meta - 元数据
   * @returns {string} - 格式化的日志
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaStr = Object.keys(meta).length > 0 ? ` | ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level}] ${message}${metaStr}`;
  }

  /**
   * 记录错误日志
   * @param {string} message - 错误消息
   * @param {Object} meta - 元数据
   */
  error(message, meta = {}) {
    if (this.currentLevel >= this.levels.ERROR) {
      console.error(this.formatMessage('ERROR', message, meta));
    }
  }

  /**
   * 记录警告日志
   * @param {string} message - 警告消息
   * @param {Object} meta - 元数据
   */
  warn(message, meta = {}) {
    if (this.currentLevel >= this.levels.WARN) {
      console.warn(this.formatMessage('WARN', message, meta));
    }
  }

  /**
   * 记录信息日志
   * @param {string} message - 信息消息
   * @param {Object} meta - 元数据
   */
  info(message, meta = {}) {
    if (this.currentLevel >= this.levels.INFO) {
      console.log(this.formatMessage('INFO', message, meta));
    }
  }

  /**
   * 记录调试日志
   * @param {string} message - 调试消息
   * @param {Object} meta - 元数据
   */
  debug(message, meta = {}) {
    if (this.currentLevel >= this.levels.DEBUG) {
      console.log(this.formatMessage('DEBUG', message, meta));
    }
  }

  /**
   * 设置日志级别
   * @param {string} level - 日志级别
   */
  setLevel(level) {
    if (this.levels[level.toUpperCase()] !== undefined) {
      this.currentLevel = this.levels[level.toUpperCase()];
    }
  }
}

// 创建全局日志实例
const logger = new Logger();

// 根据环境设置日志级别
if (process.env.NODE_ENV === 'development') {
  logger.setLevel('DEBUG');
} else if (process.env.NODE_ENV === 'production') {
  logger.setLevel('INFO');
}

module.exports = logger;
