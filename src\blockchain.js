const { ethers } = require('ethers');
const config = require('./config');

class BlockchainService {
  constructor() {
    // 初始化BSC提供商
    this.provider = new ethers.JsonRpcProvider(config.bsc.rpcUrl);
    
    // 初始化SHIB合约实例
    this.shibContract = new ethers.Contract(
      config.shib.contractAddress,
      config.erc20Abi,
      this.provider
    );
  }

  /**
   * 验证交易哈希格式
   * @param {string} txHash - 交易哈希
   * @returns {boolean} - 是否为有效格式
   */
  isValidTxHash(txHash) {
    return /^0x[a-fA-F0-9]{64}$/.test(txHash);
  }

  /**
   * 格式化SHIB金额 (从wei转换为SHIB)
   * @param {string} amount - wei格式的金额
   * @returns {string} - 格式化后的SHIB金额
   */
  formatShibAmount(amount) {
    return ethers.formatUnits(amount, config.shib.decimals);
  }

  /**
   * 获取交易详情
   * @param {string} txHash - 交易哈希
   * @returns {Object} - 交易详情
   */
  async getTransactionDetails(txHash) {
    try {
      if (!this.isValidTxHash(txHash)) {
        throw new Error('Invalid transaction hash format');
      }

      // 获取交易基本信息
      const tx = await this.provider.getTransaction(txHash);
      if (!tx) {
        throw new Error('Transaction not found');
      }

      // 获取交易收据
      const receipt = await this.provider.getTransactionReceipt(txHash);
      if (!receipt) {
        throw new Error('Transaction receipt not found');
      }

      return {
        transaction: tx,
        receipt: receipt
      };
    } catch (error) {
      console.error('Error getting transaction details:', error);
      throw error;
    }
  }

  /**
   * 解析SHIB转账事件
   * @param {Object} receipt - 交易收据
   * @returns {Array} - SHIB转账事件列表
   */
  parseShibTransfers(receipt) {
    const shibTransfers = [];
    
    // 遍历所有日志，查找SHIB转账事件
    for (const log of receipt.logs) {
      try {
        // 检查是否是SHIB合约的日志
        if (log.address.toLowerCase() === config.shib.contractAddress.toLowerCase()) {
          // 解析Transfer事件
          const parsedLog = this.shibContract.interface.parseLog(log);
          
          if (parsedLog && parsedLog.name === 'Transfer') {
            shibTransfers.push({
              from: parsedLog.args.from,
              to: parsedLog.args.to,
              value: parsedLog.args.value.toString(),
              formattedValue: this.formatShibAmount(parsedLog.args.value.toString()),
              logIndex: log.logIndex
            });
          }
        }
      } catch (error) {
        // 忽略无法解析的日志
        continue;
      }
    }
    
    return shibTransfers;
  }

  /**
   * 获取SHIB转账信息
   * @param {string} txHash - 交易哈希
   * @returns {Object} - 转账信息
   */
  async getShibTransferInfo(txHash) {
    try {
      const { transaction, receipt } = await this.getTransactionDetails(txHash);
      
      // 解析SHIB转账事件
      const shibTransfers = this.parseShibTransfers(receipt);
      
      // 获取当前区块信息
      const currentBlock = await this.provider.getBlockNumber();
      const confirmations = currentBlock - receipt.blockNumber;

      return {
        success: true,
        txHash: txHash,
        blockNumber: receipt.blockNumber,
        blockHash: receipt.blockHash,
        confirmations: confirmations,
        gasUsed: receipt.gasUsed.toString(),
        gasPrice: transaction.gasPrice ? transaction.gasPrice.toString() : '0',
        status: receipt.status === 1 ? 'success' : 'failed',
        timestamp: null, // 将在下一步获取
        shibTransfers: shibTransfers,
        totalShibTransfers: shibTransfers.length
      };
    } catch (error) {
      console.error('Error getting SHIB transfer info:', error);
      throw error;
    }
  }

  /**
   * 获取区块时间戳
   * @param {number} blockNumber - 区块号
   * @returns {number} - 时间戳
   */
  async getBlockTimestamp(blockNumber) {
    try {
      const block = await this.provider.getBlock(blockNumber);
      return block ? block.timestamp : null;
    } catch (error) {
      console.error('Error getting block timestamp:', error);
      return null;
    }
  }
}

module.exports = BlockchainService;
