/**
 * 网络连接测试工具
 * 用于测试不同的BSC RPC节点连接情况
 */

const { ethers } = require('ethers');

// 测试的RPC节点列表
const rpcNodes = [
  { name: 'BSC Official 1', url: 'https://bsc-dataseed1.binance.org/' },
  { name: 'BSC Official 2', url: 'https://bsc-dataseed2.binance.org/' },
  { name: 'BSC Official 3', url: 'https://bsc-dataseed3.binance.org/' },
  { name: 'BSC Official 4', url: 'https://bsc-dataseed4.binance.org/' },
  { name: 'Ankr', url: 'https://rpc.ankr.com/bsc' },
  { name: 'PublicNode', url: 'https://bsc.publicnode.com' },
  { name: 'NodeReal', url: 'https://bsc-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3' },
  { name: '1RPC', url: 'https://1rpc.io/bnb' },
  { name: 'BSC Testnet', url: 'https://data-seed-prebsc-1-s1.binance.org:8545/' }
];

async function testRpcNode(rpcConfig, timeout = 10000) {
  const startTime = Date.now();
  
  try {
    console.log(`\n🔍 Testing ${rpcConfig.name}: ${rpcConfig.url}`);
    
    // 创建提供商
    const provider = new ethers.JsonRpcProvider(rpcConfig.url);
    
    // 设置超时
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Timeout')), timeout);
    });
    
    // 测试基本连接 - 获取最新区块号
    const blockNumber = await Promise.race([
      provider.getBlockNumber(),
      timeoutPromise
    ]);
    
    const responseTime = Date.now() - startTime;
    
    // 测试网络信息
    const network = await Promise.race([
      provider.getNetwork(),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Network timeout')), 5000))
    ]);
    
    console.log(`✅ ${rpcConfig.name} - SUCCESS`);
    console.log(`   📊 Block Number: ${blockNumber}`);
    console.log(`   🌐 Chain ID: ${network.chainId}`);
    console.log(`   ⏱️  Response Time: ${responseTime}ms`);
    
    return {
      name: rpcConfig.name,
      url: rpcConfig.url,
      success: true,
      blockNumber,
      chainId: network.chainId.toString(),
      responseTime,
      error: null
    };
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.log(`❌ ${rpcConfig.name} - FAILED`);
    console.log(`   🚫 Error: ${error.message}`);
    console.log(`   ⏱️  Response Time: ${responseTime}ms`);
    
    return {
      name: rpcConfig.name,
      url: rpcConfig.url,
      success: false,
      blockNumber: null,
      chainId: null,
      responseTime,
      error: error.message
    };
  }
}

async function testAllNodes() {
  console.log('🚀 BSC RPC节点连接测试开始...\n');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const rpcConfig of rpcNodes) {
    const result = await testRpcNode(rpcConfig);
    results.push(result);
    
    // 等待1秒再测试下一个节点
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 测试结果汇总:');
  console.log('=' .repeat(60));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`\n✅ 成功连接: ${successful.length}/${results.length}`);
  console.log(`❌ 连接失败: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n🎉 推荐使用的RPC节点:');
    successful
      .sort((a, b) => a.responseTime - b.responseTime)
      .slice(0, 3)
      .forEach((result, index) => {
        console.log(`${index + 1}. ${result.name}`);
        console.log(`   URL: ${result.url}`);
        console.log(`   响应时间: ${result.responseTime}ms`);
        console.log(`   链ID: ${result.chainId}`);
        console.log('');
      });
      
    console.log('💡 建议将最快的RPC节点URL复制到.env文件中的BSC_RPC_URL');
  }
  
  if (failed.length > 0) {
    console.log('\n⚠️  连接失败的节点:');
    failed.forEach(result => {
      console.log(`- ${result.name}: ${result.error}`);
    });
  }
  
  return results;
}

// 如果直接运行此脚本
if (require.main === module) {
  testAllNodes().catch(console.error);
}

module.exports = { testAllNodes, testRpcNode };
