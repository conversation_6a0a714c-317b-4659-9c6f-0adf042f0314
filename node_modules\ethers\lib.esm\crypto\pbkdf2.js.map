{"version": 3, "file": "pbkdf2.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/pbkdf2.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEzC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAKtD,IAAI,MAAM,GAAG,KAAK,CAAC;AAEnB,MAAM,OAAO,GAAG,UAAS,QAAoB,EAAE,IAAgB,EAAE,UAAkB,EAAE,MAAc,EAAE,IAAyB;IAC1H,OAAO,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAChE,CAAC,CAAA;AAED,IAAI,QAAQ,GAAG,OAAO,CAAC;AAEvB;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,MAAM,CAAC,SAAoB,EAAE,KAAgB,EAAE,UAAkB,EAAE,MAAc,EAAE,IAAyB;IACxH,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACjD,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACvE,CAAC;AACD,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;AACnB,MAAM,CAAC,IAAI,GAAG,cAAmB,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;AACjD,MAAM,CAAC,QAAQ,GAAG,UAAS,IAA0H;IACjJ,IAAI,MAAM,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KAAE;IACpD,QAAQ,GAAG,IAAI,CAAC;AACpB,CAAC,CAAA;AACD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC"}