{"name": "bsc-shib-tracker", "version": "1.0.0", "description": "BSC链SHIB代币转账查询API服务", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "prod": "NODE_ENV=production node src/index.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop bsc-shib-api", "pm2:restart": "pm2 restart bsc-shib-api", "pm2:logs": "pm2 logs bsc-shib-api", "deploy": "bash deploy.sh", "test": "node proxy-test.js test"}, "keywords": ["bsc", "shib", "ethereum", "blockchain", "api", "ethers"], "author": "", "license": "MIT", "dependencies": {"ethers": "^6.8.1", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}