{"version": 3, "file": "signer-noncemanager.js", "sourceRoot": "", "sources": ["../../src.ts/providers/signer-noncemanager.ts"], "names": [], "mappings": ";;;AAAA,gDAAqD;AACrD,6DAAsD;AAUtD;;;;GAIG;AACH,MAAa,YAAa,SAAQ,mCAAc;IAC5C;;OAEG;IACH,MAAM,CAAU;IAEhB,aAAa,CAAyB;IACtC,MAAM,CAAS;IAEf;;OAEG;IACH,YAAY,MAAc;QACtB,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACvB,IAAA,2BAAgB,EAAe,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAEjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IACpC,CAAC;IAED,OAAO,CAAC,QAAyB;QAC7B,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAmB;QAC9B,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;gBAC5B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;aAClD;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;SAC7C;QAED,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,SAAS;QACL,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED;;;OAGG;IACH,KAAK;QACD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAsB;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAC/C,EAAE,CAAC,KAAK,GAAG,MAAM,YAAY,CAAC;QAE9B,sDAAsD;QACtD,wDAAwD;QACxD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,eAAe,CAAC,EAAsB;QAClC,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,WAAW,CAAC,OAA4B;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,aAAa,CAAC,MAAuB,EAAE,KAA4C,EAAE,KAA0B;QAC3G,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;CACJ;AAjFD,oCAiFC"}