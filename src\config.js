require('dotenv').config();

const config = {
  // BSC网络配置
  bsc: {
    rpcUrl: process.env.BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/',
    testnetRpcUrl: process.env.BSC_TESTNET_RPC_URL || 'https://data-seed-prebsc-1-s1.binance.org:8545/',
    chainId: 56, // BSC主网
    testnetChainId: 97 // BSC测试网
  },

  // SHIB代币配置
  shib: {
    // SHIB在BSC上的合约地址
    contractAddress: process.env.SHIB_CONTRACT_ADDRESS || '0x2859e4544C4bB03966803b044A93563Bd2D0DD4D',
    decimals: 18,
    symbol: 'SHIB',
    name: 'SHIBA INU'
  },

  // API服务配置
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },

  // ERC-20代币标准ABI (用于查询SHIB转账)
  erc20Abi: [
    "function name() view returns (string)",
    "function symbol() view returns (string)",
    "function decimals() view returns (uint8)",
    "function totalSupply() view returns (uint256)",
    "function balanceOf(address) view returns (uint256)",
    "function transfer(address to, uint256 amount) returns (bool)",
    "event Transfer(address indexed from, address indexed to, uint256 value)"
  ]
};

module.exports = config;
