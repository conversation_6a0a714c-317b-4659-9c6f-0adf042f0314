# 生产环境配置文件
# 复制此文件为 .env 并根据服务器环境调整

# BSC网络配置
BSC_RPC_URL=https://bsc-dataseed1.binance.org/
# BSC_RPC_URL=https://bsc-dataseed2.binance.org/
# BSC_RPC_URL=https://bsc-dataseed3.binance.org/
# BSC_RPC_URL=https://rpc.ankr.com/bsc
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# SHIB代币合约地址 (BSC主网)
SHIB_CONTRACT_ADDRESS=0x2859e4544C4bB03966803b044A93563Bd2D0DD4D

# 网络超时配置 (毫秒)
REQUEST_TIMEOUT=30000
RETRY_ATTEMPTS=3

# 代理配置 (生产环境通常不需要代理)
USE_PROXY=false
# PROXY_URL=http://127.0.0.1:7890

# API服务配置
PORT=3000
NODE_ENV=production

# 可选：如果需要使用特定的RPC提供商
# INFURA_PROJECT_ID=your_infura_project_id
# ALCHEMY_API_KEY=your_alchemy_api_key
