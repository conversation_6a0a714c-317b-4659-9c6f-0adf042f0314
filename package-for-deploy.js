/**
 * 项目打包脚本
 * 创建适合上传到宝塔服务器的压缩包
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 需要包含的文件和目录
const includeFiles = [
  'src/',
  'package.json',
  'package-lock.json',
  '.env.production',
  'ecosystem.config.js',
  'deploy.sh',
  'health-check.js',
  'README.md',
  'BAOTA_DEPLOY.md',
  'examples.md'
];

// 需要排除的文件和目录
const excludeFiles = [
  'node_modules/',
  '.env',
  'logs/',
  '*.log',
  '.git/',
  'test.js',
  'proxy-test.js',
  'setup-proxy.js',
  'network-test.js',
  'package-for-deploy.js'
];

function createDeployPackage() {
  console.log('📦 开始创建部署包...\n');
  
  const deployDir = 'deploy-package';
  const zipName = 'bsc-shib-api-deploy.zip';
  
  try {
    // 创建临时目录
    if (fs.existsSync(deployDir)) {
      execSync(`rm -rf ${deployDir}`, { stdio: 'inherit' });
    }
    fs.mkdirSync(deployDir);
    
    console.log('📋 复制项目文件...');
    
    // 复制需要的文件
    includeFiles.forEach(file => {
      const srcPath = path.join('.', file);
      const destPath = path.join(deployDir, file);
      
      if (fs.existsSync(srcPath)) {
        const stat = fs.statSync(srcPath);
        
        if (stat.isDirectory()) {
          // 复制目录
          execSync(`cp -r "${srcPath}" "${destPath}"`, { stdio: 'inherit' });
          console.log(`✅ 复制目录: ${file}`);
        } else {
          // 复制文件
          fs.copyFileSync(srcPath, destPath);
          console.log(`✅ 复制文件: ${file}`);
        }
      } else {
        console.log(`⚠️  文件不存在: ${file}`);
      }
    });
    
    // 创建部署说明文件
    const deployInstructions = `# BSC SHIB API 部署包

## 📦 包含文件
${includeFiles.map(file => `- ${file}`).join('\n')}

## 🚀 部署步骤

1. **上传到宝塔服务器**
   - 解压到 /www/wwwroot/bsc-shib-api/

2. **运行部署脚本**
   \`\`\`bash
   cd /www/wwwroot/bsc-shib-api
   chmod +x deploy.sh
   ./deploy.sh
   \`\`\`

3. **配置环境变量**
   \`\`\`bash
   cp .env.production .env
   vi .env  # 根据需要修改配置
   \`\`\`

4. **启动服务**
   \`\`\`bash
   npm run pm2:start
   \`\`\`

5. **测试服务**
   \`\`\`bash
   node health-check.js
   \`\`\`

## 📖 详细说明
请查看 BAOTA_DEPLOY.md 文件获取完整的部署指南。

---
生成时间: ${new Date().toISOString()}
`;
    
    fs.writeFileSync(path.join(deployDir, 'DEPLOY_INSTRUCTIONS.md'), deployInstructions);
    console.log('✅ 创建部署说明文件');
    
    // 创建压缩包
    console.log('\n📦 创建压缩包...');
    
    // 删除旧的压缩包
    if (fs.existsSync(zipName)) {
      fs.unlinkSync(zipName);
    }
    
    // 创建新的压缩包
    execSync(`cd ${deployDir} && zip -r ../${zipName} .`, { stdio: 'inherit' });
    
    // 清理临时目录
    execSync(`rm -rf ${deployDir}`, { stdio: 'inherit' });
    
    console.log(`\n🎉 部署包创建成功: ${zipName}`);
    console.log('\n📋 下一步操作:');
    console.log(`1. 将 ${zipName} 上传到宝塔服务器`);
    console.log('2. 解压到 /www/wwwroot/bsc-shib-api/');
    console.log('3. 按照 DEPLOY_INSTRUCTIONS.md 进行部署');
    
    // 显示文件大小
    const stats = fs.statSync(zipName);
    const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`\n📊 压缩包大小: ${fileSizeInMB} MB`);
    
  } catch (error) {
    console.error('❌ 创建部署包失败:', error.message);
    process.exit(1);
  }
}

function showPackageInfo() {
  console.log('📦 BSC SHIB API 部署包创建工具\n');
  console.log('此工具将创建一个包含所有必要文件的压缩包，');
  console.log('可以直接上传到宝塔服务器进行部署。\n');
  
  console.log('📋 将包含以下文件:');
  includeFiles.forEach(file => {
    console.log(`  ✅ ${file}`);
  });
  
  console.log('\n🚫 将排除以下文件:');
  excludeFiles.forEach(file => {
    console.log(`  ❌ ${file}`);
  });
  
  console.log('\n');
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

if (require.main === module) {
  if (command === 'info') {
    showPackageInfo();
  } else {
    showPackageInfo();
    createDeployPackage();
  }
}

module.exports = { createDeployPackage };
