{"version": 3, "file": "signing-key.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/signing-key.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,uDAAoD;AAEpD,gDAG2B;AAE3B,iDAA2C;AAO3C;;;GAGG;AACH,MAAa,UAAU;IACnB,WAAW,CAAS;IAEpB;;OAEG;IACH,YAAY,UAAqB;QAC7B,IAAA,yBAAc,EAAC,IAAA,qBAAU,EAAC,UAAU,CAAC,KAAK,EAAE,EAAE,qBAAqB,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QACjG,IAAI,CAAC,WAAW,GAAG,IAAA,kBAAO,EAAC,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,UAAU,KAAa,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAErD;;;;;OAKG;IACH,IAAI,SAAS,KAAa,OAAO,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEjF;;;;;;OAMG;IACH,IAAI,mBAAmB,KAAa,OAAO,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAEjG;;OAEG;IACH,IAAI,CAAC,MAAiB;QAClB,IAAA,yBAAc,EAAC,IAAA,qBAAU,EAAC,MAAM,CAAC,KAAK,EAAE,EAAE,uBAAuB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErF,MAAM,GAAG,GAAG,qBAAS,CAAC,IAAI,CAAC,IAAA,uBAAY,EAAC,MAAM,CAAC,EAAE,IAAA,uBAAY,EAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC7E,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,OAAO,wBAAS,CAAC,IAAI,CAAC;YAClB,CAAC,EAAE,IAAA,kBAAO,EAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;YACrB,CAAC,EAAE,IAAA,kBAAO,EAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;YACrB,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAA,CAAC,CAAC,IAAI,CAAC;SACjC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,mBAAmB,CAAC,KAAgB;QAChC,MAAM,MAAM,GAAG,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAClD,OAAO,IAAA,kBAAO,EAAC,qBAAS,CAAC,eAAe,CAAC,IAAA,uBAAY,EAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAA,mBAAQ,EAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACvG,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAc,EAAE,UAAoB;QACxD,IAAI,KAAK,GAAG,IAAA,mBAAQ,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAEjC,cAAc;QACd,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;YACrB,MAAM,MAAM,GAAG,qBAAS,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;YAC3D,OAAO,IAAA,kBAAO,EAAC,MAAM,CAAC,CAAC;SAC1B;QAED,wDAAwD;QACxD,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;YACrB,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;YAC/B,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YACd,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClB,KAAK,GAAG,GAAG,CAAC;SACf;QAED,MAAM,KAAK,GAAG,qBAAS,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,OAAO,IAAA,kBAAO,EAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAiB,EAAE,SAAwB;QAC/D,IAAA,yBAAc,EAAC,IAAA,qBAAU,EAAC,MAAM,CAAC,KAAK,EAAE,EAAE,uBAAuB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErF,MAAM,GAAG,GAAG,wBAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAI,OAAO,GAAG,qBAAS,CAAC,SAAS,CAAC,WAAW,CAAC,IAAA,uBAAY,EAAC,IAAA,iBAAM,EAAC,CAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC;QACtF,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAA,uBAAY,EAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,IAAA,yBAAc,EAAC,MAAM,IAAI,IAAI,EAAE,8BAA8B,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAEvF,OAAO,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,SAAS,CAAC,EAAa,EAAE,EAAa,EAAE,UAAoB;QAC/D,MAAM,IAAI,GAAG,qBAAS,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,MAAM,IAAI,GAAG,qBAAS,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;IACpD,CAAC;CACJ;AA1KD,gCA0KC"}