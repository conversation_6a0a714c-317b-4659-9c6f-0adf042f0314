/**
 * 健康检查脚本
 * 用于监控API服务状态
 */

const http = require('http');
const https = require('https');

const config = {
  host: process.env.HEALTH_CHECK_HOST || 'localhost',
  port: process.env.PORT || 3000,
  timeout: 5000
};

function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(config.timeout, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

async function checkHealth() {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] 开始健康检查...`);
  
  try {
    // 检查健康状态接口
    const healthResponse = await makeRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/health',
      method: 'GET',
      timeout: config.timeout
    });
    
    if (healthResponse.statusCode === 200) {
      const healthData = JSON.parse(healthResponse.data);
      console.log(`✅ 健康检查通过: ${healthData.message}`);
      
      // 检查API信息接口
      const infoResponse = await makeRequest({
        hostname: config.host,
        port: config.port,
        path: '/api/info',
        method: 'GET',
        timeout: config.timeout
      });
      
      if (infoResponse.statusCode === 200) {
        console.log('✅ API信息接口正常');
        return { success: true, message: 'All checks passed' };
      } else {
        console.log(`❌ API信息接口异常: ${infoResponse.statusCode}`);
        return { success: false, message: 'Info endpoint failed' };
      }
    } else {
      console.log(`❌ 健康检查失败: HTTP ${healthResponse.statusCode}`);
      return { success: false, message: `Health check failed: ${healthResponse.statusCode}` };
    }
    
  } catch (error) {
    console.log(`❌ 健康检查错误: ${error.message}`);
    return { success: false, message: error.message };
  }
}

async function checkBSCConnection() {
  console.log('🔍 检查BSC网络连接...');
  
  try {
    const { ethers } = require('ethers');
    const config = require('./src/config');
    
    const provider = new ethers.JsonRpcProvider(config.bsc.rpcUrl);
    const blockNumber = await provider.getBlockNumber();
    
    console.log(`✅ BSC网络连接正常，当前区块: ${blockNumber}`);
    return { success: true, blockNumber };
  } catch (error) {
    console.log(`❌ BSC网络连接失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function fullHealthCheck() {
  console.log('🚀 开始完整健康检查...\n');
  
  const results = {
    timestamp: new Date().toISOString(),
    api: await checkHealth(),
    bsc: await checkBSCConnection()
  };
  
  console.log('\n📊 健康检查结果:');
  console.log('='.repeat(40));
  console.log(`API服务: ${results.api.success ? '✅ 正常' : '❌ 异常'}`);
  console.log(`BSC连接: ${results.bsc.success ? '✅ 正常' : '❌ 异常'}`);
  
  if (results.api.success && results.bsc.success) {
    console.log('\n🎉 所有检查通过，服务运行正常！');
    process.exit(0);
  } else {
    console.log('\n⚠️ 发现问题，请检查服务状态');
    if (!results.api.success) {
      console.log(`API问题: ${results.api.message}`);
    }
    if (!results.bsc.success) {
      console.log(`BSC问题: ${results.bsc.error}`);
    }
    process.exit(1);
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

if (require.main === module) {
  if (command === 'api') {
    checkHealth().then(result => {
      process.exit(result.success ? 0 : 1);
    });
  } else if (command === 'bsc') {
    checkBSCConnection().then(result => {
      process.exit(result.success ? 0 : 1);
    });
  } else {
    fullHealthCheck();
  }
}

module.exports = { checkHealth, checkBSCConnection, fullHealthCheck };
