const express = require('express');
const TransferService = require('./transferService');

const router = express.Router();
const transferService = new TransferService();

/**
 * 健康检查接口
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'BSC SHIB Transfer API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

/**
 * 获取API信息
 */
router.get('/info', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'BSC SHIB Transfer Tracker',
      version: '1.0.0',
      description: 'API for querying SHIB token transfers on BSC network',
      network: 'Binance Smart Chain (BSC)',
      token: 'SHIBA INU (SHIB)',
      endpoints: {
        'GET /api/health': 'Health check',
        'GET /api/info': 'API information',
        'GET /api/transfer/:hash': 'Get transfer data by transaction hash',
        'POST /api/transfer/batch': 'Get transfer data for multiple transaction hashes'
      }
    }
  });
});

/**
 * 根据交易哈希获取SHIB转账数据
 */
router.get('/transfer/:hash', async (req, res) => {
  try {
    const { hash } = req.params;
    
    console.log(`API request for transaction hash: ${hash}`);
    
    const result = await transferService.getTransferData(hash);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      data: null
    });
  }
});

/**
 * 批量查询转账数据
 */
router.post('/transfer/batch', async (req, res) => {
  try {
    const { hashes } = req.body;
    
    if (!hashes) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Transaction hashes array is required',
          code: 'MISSING_PARAMETER'
        },
        data: null
      });
    }

    console.log(`API batch request for ${hashes.length} transaction hashes`);
    
    const result = await transferService.getBatchTransferData(hashes);
    res.json(result);
    
  } catch (error) {
    console.error('API Batch Error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        code: 'BATCH_ERROR'
      },
      data: null
    });
  }
});

/**
 * 错误处理中间件
 */
router.use((error, req, res, next) => {
  console.error('Unhandled route error:', error);
  res.status(500).json({
    success: false,
    error: {
      message: 'Internal server error',
      code: 'INTERNAL_ERROR'
    },
    data: null
  });
});

module.exports = router;
