/**
 * 简单的API测试脚本
 */

const axios = require('axios').default;

const BASE_URL = 'http://localhost:3000/api';

// 测试用的SHIB转账交易哈希 (这些是示例，需要替换为真实的交易哈希)
const TEST_HASHES = [
  // 请替换为真实的BSC上SHIB转账交易哈希
  '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
  '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890'
];

async function testAPI() {
  console.log('🧪 开始测试 BSC SHIB Transfer Tracker API\n');

  try {
    // 1. 测试健康检查
    console.log('1️⃣ 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查通过:', healthResponse.data.message);
    console.log('');

    // 2. 测试API信息
    console.log('2️⃣ 测试API信息...');
    const infoResponse = await axios.get(`${BASE_URL}/info`);
    console.log('✅ API信息获取成功:', infoResponse.data.data.name);
    console.log('');

    // 3. 测试单个转账查询 (使用示例哈希)
    console.log('3️⃣ 测试单个转账查询...');
    try {
      const transferResponse = await axios.get(`${BASE_URL}/transfer/${TEST_HASHES[0]}`);
      console.log('✅ 转账查询成功:', transferResponse.data);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('⚠️ 预期的错误 (测试哈希):', error.response.data.error.message);
      } else {
        throw error;
      }
    }
    console.log('');

    // 4. 测试批量查询
    console.log('4️⃣ 测试批量转账查询...');
    try {
      const batchResponse = await axios.post(`${BASE_URL}/transfer/batch`, {
        hashes: TEST_HASHES
      });
      console.log('✅ 批量查询成功:', batchResponse.data);
    } catch (error) {
      if (error.response) {
        console.log('⚠️ 批量查询响应:', error.response.data);
      } else {
        throw error;
      }
    }
    console.log('');

    // 5. 测试无效哈希
    console.log('5️⃣ 测试无效哈希处理...');
    try {
      await axios.get(`${BASE_URL}/transfer/invalid_hash`);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 无效哈希错误处理正确:', error.response.data.error.message);
      }
    }
    console.log('');

    console.log('🎉 所有测试完成！');
    console.log('\n📝 使用说明:');
    console.log('1. 将 test.js 中的 TEST_HASHES 替换为真实的BSC SHIB转账交易哈希');
    console.log('2. 确保服务器正在运行 (npm start 或 npm run dev)');
    console.log('3. 运行测试: node test.js');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请确保API服务器正在运行');
      console.log('   运行命令: npm start 或 npm run dev');
    }
  }
}

// 检查是否直接运行此脚本
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };
