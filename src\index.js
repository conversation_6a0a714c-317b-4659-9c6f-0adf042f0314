const express = require('express');
const cors = require('cors');
const config = require('./config');
const routes = require('./routes');
const logger = require('./logger');

// 创建Express应用
const app = express();

// 中间件配置
app.use(cors()); // 启用CORS
app.use(express.json({ limit: '10mb' })); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码请求体

// 请求日志中间件
app.use((req, res, next) => {
  const start = Date.now();
  
  // 记录请求开始
  logger.info(`${req.method} ${req.url}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info(`${req.method} ${req.url} - ${res.statusCode}`, {
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length') || 0
    });
  });

  next();
});

// API路由
app.use('/api', routes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'BSC SHIB Transfer Tracker API',
    version: '1.0.0',
    documentation: {
      health: 'GET /api/health',
      info: 'GET /api/info',
      transfer: 'GET /api/transfer/:hash',
      batchTransfer: 'POST /api/transfer/batch'
    },
    examples: {
      singleTransfer: '/api/transfer/0x1234567890abcdef...',
      batchTransfer: {
        url: '/api/transfer/batch',
        method: 'POST',
        body: {
          hashes: ['0x1234...', '0x5678...']
        }
      }
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      message: 'Endpoint not found',
      code: 'NOT_FOUND'
    },
    data: null
  });
});

// 全局错误处理中间件
app.use((error, req, res, next) => {
  logger.error('Unhandled application error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method
  });

  res.status(500).json({
    success: false,
    error: {
      message: 'Internal server error',
      code: 'INTERNAL_ERROR'
    },
    data: null
  });
});

// 启动服务器
const PORT = config.server.port;

app.listen(PORT, () => {
  logger.info(`BSC SHIB Transfer Tracker API started`, {
    port: PORT,
    environment: config.server.env,
    bscRpc: config.bsc.rpcUrl,
    shibContract: config.shib.contractAddress
  });
  
  console.log(`
🚀 BSC SHIB Transfer Tracker API is running!

📍 Server: http://localhost:${PORT}
📖 API Info: http://localhost:${PORT}/api/info
❤️  Health Check: http://localhost:${PORT}/api/health

🔍 Example Usage:
   GET  http://localhost:${PORT}/api/transfer/YOUR_TX_HASH
   POST http://localhost:${PORT}/api/transfer/batch

🌐 Network: BSC (Binance Smart Chain)
🪙 Token: SHIB (${config.shib.contractAddress})
  `);
});

// 优雅关闭处理
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection:', {
    reason: reason,
    promise: promise
  });
});

module.exports = app;
