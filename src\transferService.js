const BlockchainService = require('./blockchain');

class TransferService {
  constructor() {
    this.blockchainService = new BlockchainService();
  }

  /**
   * 获取完整的转账信息
   * @param {string} txHash - 交易哈希
   * @returns {Object} - 完整的转账信息
   */
  async getTransferData(txHash) {
    try {
      // 验证输入
      if (!txHash) {
        throw new Error('Transaction hash is required');
      }

      if (!this.blockchainService.isValidTxHash(txHash)) {
        throw new Error('Invalid transaction hash format');
      }

      console.log(`Querying transfer data for hash: ${txHash}`);

      // 获取SHIB转账信息
      const transferInfo = await this.blockchainService.getShibTransferInfo(txHash);

      // 获取区块时间戳
      if (transferInfo.blockNumber) {
        const timestamp = await this.blockchainService.getBlockTimestamp(transferInfo.blockNumber);
        transferInfo.timestamp = timestamp;
        transferInfo.date = timestamp ? new Date(timestamp * 1000).toISOString() : null;
      }

      // 格式化返回数据
      const result = {
        success: true,
        data: {
          transactionHash: txHash,
          blockNumber: transferInfo.blockNumber,
          blockHash: transferInfo.blockHash,
          confirmations: transferInfo.confirmations,
          status: transferInfo.status,
          timestamp: transferInfo.timestamp,
          date: transferInfo.date,
          gasUsed: transferInfo.gasUsed,
          gasPrice: transferInfo.gasPrice,
          shibTransfers: transferInfo.shibTransfers.map(transfer => ({
            from: transfer.from,
            to: transfer.to,
            amount: transfer.formattedValue,
            amountWei: transfer.value,
            token: 'SHIB',
            logIndex: transfer.logIndex
          })),
          totalTransfers: transferInfo.totalShibTransfers,
          network: 'BSC',
          tokenContract: this.blockchainService.shibContract.target
        },
        message: transferInfo.totalShibTransfers > 0 
          ? `Found ${transferInfo.totalShibTransfers} SHIB transfer(s)` 
          : 'No SHIB transfers found in this transaction'
      };

      console.log(`Successfully retrieved transfer data: ${transferInfo.totalShibTransfers} transfers found`);
      return result;

    } catch (error) {
      console.error('Error in getTransferData:', error);
      
      // 返回错误信息
      return {
        success: false,
        error: {
          message: error.message,
          code: this.getErrorCode(error.message)
        },
        data: null
      };
    }
  }

  /**
   * 根据错误消息获取错误代码
   * @param {string} errorMessage - 错误消息
   * @returns {string} - 错误代码
   */
  getErrorCode(errorMessage) {
    if (errorMessage.includes('Invalid transaction hash')) {
      return 'INVALID_HASH';
    }
    if (errorMessage.includes('Transaction not found')) {
      return 'TX_NOT_FOUND';
    }
    if (errorMessage.includes('Transaction receipt not found')) {
      return 'RECEIPT_NOT_FOUND';
    }
    if (errorMessage.includes('required')) {
      return 'MISSING_PARAMETER';
    }
    return 'UNKNOWN_ERROR';
  }

  /**
   * 批量查询转账数据
   * @param {Array} txHashes - 交易哈希数组
   * @returns {Array} - 转账数据数组
   */
  async getBatchTransferData(txHashes) {
    if (!Array.isArray(txHashes)) {
      throw new Error('Transaction hashes must be an array');
    }

    if (txHashes.length === 0) {
      throw new Error('At least one transaction hash is required');
    }

    if (txHashes.length > 10) {
      throw new Error('Maximum 10 transaction hashes allowed per batch');
    }

    const results = [];
    
    for (const txHash of txHashes) {
      try {
        const result = await this.getTransferData(txHash);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          error: {
            message: error.message,
            code: this.getErrorCode(error.message)
          },
          data: null,
          txHash: txHash
        });
      }
    }

    return {
      success: true,
      results: results,
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    };
  }
}

module.exports = TransferService;
