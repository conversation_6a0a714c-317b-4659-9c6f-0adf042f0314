#!/bin/bash

# BSC SHIB API 部署脚本
# 适用于宝塔服务器

echo "🚀 开始部署 BSC SHIB API..."

# 创建日志目录
mkdir -p logs

# 安装依赖
echo "📦 安装项目依赖..."
npm install --production

# 复制生产环境配置
if [ ! -f .env ]; then
    echo "📋 创建生产环境配置..."
    cp .env.production .env
    echo "⚠️  请编辑 .env 文件以配置您的环境变量"
fi

# 测试配置
echo "🔍 测试网络连接..."
node -e "
const { ethers } = require('ethers');
const config = require('./src/config');

async function test() {
  try {
    const provider = new ethers.JsonRpcProvider(config.bsc.rpcUrl);
    const blockNumber = await provider.getBlockNumber();
    console.log('✅ BSC网络连接成功，当前区块:', blockNumber);
  } catch (error) {
    console.log('❌ BSC网络连接失败:', error.message);
    console.log('💡 请检查网络连接或更换RPC节点');
  }
}
test();
"

echo "✅ 部署准备完成！"
echo ""
echo "📋 下一步操作："
echo "1. 编辑 .env 文件配置环境变量"
echo "2. 使用 PM2 启动应用: pm2 start ecosystem.config.js"
echo "3. 设置开机自启: pm2 startup && pm2 save"
echo "4. 配置反向代理 (可选)"
echo ""
